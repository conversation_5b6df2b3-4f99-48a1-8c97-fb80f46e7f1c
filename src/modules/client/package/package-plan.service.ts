import { Injectable, } from '@nestjs/common';
import { PackagePlanListDto } from './dto/package.dto';
import { BindRepo, } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';

@Injectable()
export class PackagePlanService {
  constructor() {
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  async findAll() {
    return this.packagePlanRepo.find({ where: { status: NSPackage.EStatus.ACTIVE }, order: { sellPriceMonthly: 'ASC' } });
  }

  async findPagination(body: PackagePlanListDto) {
    const { name, status, packagePlanId, pageIndex, pageSize } = body;
    const where: any = { status: NSPackage.EStatus.ACTIVE }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    return await this.packagePlanRepo.findPagination({ where, order: { sellPriceMonthly: 'ASC' } }, { pageIndex, pageSize });
  }

}
