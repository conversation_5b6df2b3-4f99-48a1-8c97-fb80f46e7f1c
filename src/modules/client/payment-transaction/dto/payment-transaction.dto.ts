import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSPackage } from '~/common/enums';
import { NSPayment } from '~/common/enums/payment.enum';

export class CreatePaymentTransactionDto {
  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsUUID()
  orderId: string;

  @ApiProperty({ description: 'Số tiền giao dịch thực tế (bao gồm giảm giá)' })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({ description: 'Số tiền giao dịch thực tế (bao gồm giảm giá)' })
  @IsNumber()
  @IsNotEmpty()
  amountVat: number;

  @ApiProperty({ description: 'Tổng số tiền giao dịch (trướ<PERSON><PERSON> mãi)' })
  @IsNumber()
  grossAmount: number;

  @ApiProperty({ description: 'Đối tác cổng thanh toán' })
  @IsEnum(NSPayment.EPaymentProvider)
  paymentProvider: NSPayment.EPaymentProvider;

  @ApiProperty({ description: 'Phương thức thanh toán' })
  @IsEnum(NSPayment.EPaymentMethod)
  paymentMethod: NSPayment.EPaymentMethod;

  // Tự động gia hạn
  @ApiPropertyOptional({ description: 'Tự động gia hạn' })
  @IsOptional()
  isAutoRenewal?: boolean;

  @ApiPropertyOptional({ description: 'ID giao dịch trên cổng thanh toán' })
  @IsOptional()
  refId?: string;

  @ApiPropertyOptional({ description: 'Client secret của Stripe' })
  @IsOptional()
  clientSecret?: string;

  @ApiProperty({ description: 'Thời gian đăng ký (tháng/năm)' })
  @IsNumber()
  @IsOptional()
  timeRegister: number = 1;

  @ApiProperty({ description: 'MONTHLY/YEARLY' })
  @IsEnum(NSPackage.EPlanTypePayment)
  @IsNotEmpty()
  unit: NSPackage.EPlanTypePayment;
}

export class PaymentTransactionListDto extends PageRequest {
  @ApiProperty({ description: 'Mã code thanh toán' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'ID của member thanh toán (member sở hữu thẻ)' })
  @IsOptional()
  memberId?: string;

  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsOptional()
  orderId?: string;

  @ApiProperty({ description: 'Trạng thái thanh toán' })
  @IsOptional()
  status?: NSPayment.ETransactionStatus;

  @ApiProperty({ description: 'Ngày thanh toán từ' })
  @IsOptional()
  transactionDateFrom?: Date;

  @ApiProperty({ description: 'Ngày thanh toán đến' })
  @IsOptional()
  transactionDateTo?: Date;
}

//amount: number, currency = 'usd', orderId: string, transactionId: string
export class PaymentIntentDto {
  @ApiProperty({ description: 'Số tiền giao dịch' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  @IsString()
  currency: string = 'usd';

  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsUUID()
  orderId: string;

  @ApiProperty({ description: 'ID của giao dịch' })
  @IsUUID()
  transactionId: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  memberPackageId: string;

  @ApiProperty({ description: 'Loại gói dịch vụ' })
  @IsEnum(NSPackage.EPlanTypePayment)
  paymentType: NSPackage.EPlanTypePayment;
}

export class PaymentSubscriptionDto {
  //memberId optional
  @ApiPropertyOptional({ description: 'ID của thành viên' })
  @IsOptional()
  memberId?: string;

  @ApiProperty({ description: 'ID của đơn hàng' })
  @IsUUID()
  orderId: string;

  @ApiProperty({ description: 'ID của giao dịch' })
  @IsUUID()
  transactionId: string;

  @ApiProperty({ description: 'Email của khách hàng' })
  @IsString()
  customerEmail: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  @IsOptional()
  oldPlanId?: string;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  newPlanId: string;

  @ApiProperty({ description: 'Loại gói dịch vụ' })
  @IsEnum(NSPackage.EPlanTypePayment)
  paymentType: NSPackage.EPlanTypePayment;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsUUID()
  memberPackageId: string;

  @ApiProperty({ description: 'Tự động gia hạn' })
  @IsBoolean()
  @IsOptional()
  isAutoRenewal?: boolean;

  @ApiProperty({ description: 'Số tiền thanh toán' })
  @IsNumber()
  @IsOptional()
  amount?: number;

  @ApiProperty({ description: 'Thời gian đăng ký (tháng/năm)' })
  @IsNumber()
  @IsOptional()
  timeRegister?: number = 1;

  //type
  @ApiProperty({ description: 'Loại giao dịch' })
  @IsOptional()
  @IsEnum(NSPayment.ETransactionType)
  transactionType?: NSPayment.ETransactionType;
}

export class InvoiceDetailDto {
  @ApiProperty({ description: 'ID của hóa đơn' })
  @IsString()
  @IsNotEmpty()
  invoiceId: string;
}
