import { Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { PaymentTransactionRepo } from '~/domains/primary/payment-transaction/payment-transaction.repo';
import {
  CreatePaymentTransactionDto,
  InvoiceDetailDto,
  PaymentSubscriptionDto,
  PaymentTransactionListDto,
} from './dto/payment-transaction.dto';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { NSPayment } from '~/common/enums/payment.enum';
import { OrderRepo } from '~/domains/primary/order/order.repo';
import { OrderItemRepo, PackagePlanRepo } from '~/domains/primary/';
import { MemberRepo } from '~/domains/primary/member/member.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { memberSessionContext } from '../member-session.context';
import { NSMember, NSOrder, NSPackage } from '~/common/enums';
import { BusinessException } from '~/@systems/exceptions';
import * as dayjs from 'dayjs';
import { MemberPackageDto } from '../member-package/dto/member-package.dto';
import { numberHelper } from '~/common/helpers/number.helper';

@Injectable()
export class PaymentTransactionService {
  private stripe: Stripe;
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(OrderItemRepo)
  private orderItemsRepo: OrderItemRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @DefTransaction()
  async create(transaction: CreatePaymentTransactionDto) {
    const { memberId } = memberSessionContext;
    const { orderId, paymentProvider, isAutoRenewal, amount, amountVat, unit, timeRegister } =
      transaction;
    const [order, planPackage, member] = await Promise.all([
      this.orderRepo.findOne({ where: { id: orderId } }),
      this.orderItemsRepo.findOne({ where: { orderId } }),
      this.memberRepo.findOne({ where: { id: memberId } }),
    ]);

    if (!order) throw new BusinessException('payment.order_not_found');
    if (!planPackage) throw new BusinessException('payment.plan_not_found');
    if (!member) throw new BusinessException('payment.member_not_found');

    // Check timeRegister and unit is Yearly only allowed 2 years
    if (
      unit === NSPackage.EPlanTypePayment.YEARLY &&
      timeRegister > 2 &&
      paymentProvider === NSPayment.EPaymentProvider.STRIPE
    ) {
      throw new BusinessException('payment_stripe.max_2_years_only');
    }

    const { REDIRECT_URL_PAYMENT } = configEnv();

    const plan = await this.packagePlanRepo.findOne({ where: { id: planPackage.packagePlanId } });
    if (!plan) throw new BusinessException('payment.plan_not_found');

    let newMemberPackage = null;
    let checkMemberPackage: any = await this.memberPackageRepo.findOne({
      where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE },
    });

    const memberPackage: any = this.memberPackageRepo.create({
      memberId,
      packagePlanId: plan.id,
      unit,
      initialTransactionLimit: unit === NSPackage.EPlanTypePayment.MONTHLY
        ? plan.transactionLimit
        : plan.transactionLimitYearly, // Each month will be more
      initialConfigLimit: unit === NSPackage.EPlanTypePayment.MONTHLY
        ? plan.configLimit
        : plan.configLimitYearly, // Total configuration is more
      projectLimit: unit === NSPackage.EPlanTypePayment.MONTHLY
        ? plan.projectLimit
        : plan.projectLimitYearly, // Total projects is more
      currentProject: 0,
      currentTransaction: 0,
      currentConfig: 0,
      status: 'PENDING',
      byteLimit: unit === NSPackage.EPlanTypePayment.MONTHLY ? plan.byteLimit : plan.byteLimitYearly,
      buyPrice: unit === NSPackage.EPlanTypePayment.MONTHLY ? plan.sellPriceMonthly : plan.sellPriceYearly,
    });
    if (checkMemberPackage) {
      //Upgrade Member Package
      if (plan.transactionLimit > checkMemberPackage.initialTransactionLimit) {
        return this.upgradeMemberPackage(transaction, checkMemberPackage, memberPackage);
      } else if (plan.transactionLimit < checkMemberPackage.initialTransactionLimit) {
        return this.downgradeMemberPackage(transaction, checkMemberPackage, memberPackage);
      } else {
        return this.extendMemberPackage(transaction, checkMemberPackage);
      }
      //Downgrade Member Package
    } else {
      newMemberPackage = await this.memberPackageRepo.save(memberPackage);

      if (plan.isTrial) {
        // Update member package
        await this.memberPackageRepo.update(
          { id: newMemberPackage.id },
          {
            status: NSMember.EMemberPackageStatus.ACTIVE,
            activatedDate: new Date().toISOString(),
            expiredDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
          },
        );
        return {
          checkoutUrl: `${REDIRECT_URL_PAYMENT}/payment-success`,
          sessionId: '',
        };
      }

      // Update order value and vat
      const vat = +amountVat - +amount;

      // 2. Tạo payment transaction
      const paymentTransaction = this.paymentTransactionRepo.create({
        ...transaction,
        memberId,
        amount: amountVat,
        grossAmount: amountVat,
        memberPackageId: newMemberPackage.id,
      });
      const savedTransaction = await this.paymentTransactionRepo.save(paymentTransaction);
      const transactionId = savedTransaction.id;

      // Recalculate order value
      await this.orderRepo.update(
        { id: orderId },
        {
          status: NSOrder.EStatus.PENDING,
          totalPrice: amount,
          totalPriceVat: amountVat,
          vat,
        },
      );

      // 4. If Stripe then create checkout session
      if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
        // Create checkout session
        const checkoutSession = await this.createStripeCheckoutSession({
          orderId,
          transactionId,
          customerEmail: member.email,
          newPlanId: planPackage.packagePlanId,
          paymentType: unit,
          memberPackageId: newMemberPackage.id,
          isAutoRenewal,
          amount: +amountVat,
          timeRegister,
          transactionType: NSPayment.ETransactionType.CREATE,
        });

        // Update transaction with sessionId
        if (checkoutSession.sessionId) {
          await this.paymentTransactionRepo.update(transactionId, {
            sessionRefId: checkoutSession.sessionId,
          });
        }

        return {
          ...savedTransaction,
          memberPackage: newMemberPackage,
          checkoutUrl: checkoutSession?.checkoutUrl,
          sessionId: checkoutSession?.sessionId,
        };
      }
    }
  }

  // Handle service package renewal (increase expired time)
  async extendMemberPackage(
    transaction: CreatePaymentTransactionDto,
    currentPackage: MemberPackageDto,
  ) {
    const { orderId, paymentProvider, isAutoRenewal, amount, amountVat, unit } = transaction;

    //check orderType
    const order = await this.orderRepo.findOne({ where: { id: orderId } });
    if (!order) throw new BusinessException('payment.order_not_found');
    if (order.orderType != NSOrder.EOrderType.RENEWAL) {
      throw new BusinessException('payment.order_type_invalid'); // Go to dashboard to view your plan
    }

    const checkCurrentPackage = await this.memberPackageRepo.findOne({
      where: { id: currentPackage.id, status: NSMember.EMemberPackageStatus.ACTIVE },
    });
    if (!checkCurrentPackage) throw new BusinessException('payment.service_package_invalid');

    const currentPlan = await this.packagePlanRepo.findOne({
      where: { id: currentPackage.packagePlanId },
    });

    // Check if current package has auto-renewal subscription
    if (currentPackage.isAutoRenewal) {
      throw new BusinessException('payment.cannot_renew_auto_renewal_package');
    }

    // Error if the service package you are using has been hard deleted (this is an error that cannot occur, system error)
    if (!currentPlan) throw new BusinessException('payment.service_package_not_found');

    const member = await this.memberRepo.findOne({ where: { id: currentPackage.memberId } });
    if (!member) throw new BusinessException('payment.member_not_found');

    // //reset lại currentPackage
    // await this.memberPackageRepo.update(
    //   { id: currentPackage.id },
    //   {
    //     orderId,
    //     status: NSMember.EMemberPackageStatus.PENDING,
    //     expiredDate: dayjs(currentPackage.expiredDate).add(1, 'month').toISOString(),
    //   },
    // );

    // Update order value and vat
    const vat = +amountVat - +amount;

    // 2. Create payment transaction
    const paymentTransaction = this.paymentTransactionRepo.create({
      ...transaction,
      memberId: member.id,
      amount: amountVat,
      grossAmount: amountVat,
      memberPackageId: currentPlan.id,
      transactionType: NSPayment.ETransactionType.RENEWAL,
    });

    const savedTransaction = await this.paymentTransactionRepo.save(paymentTransaction);
    const transactionId = savedTransaction.id;

    // Recalculate order value
    await this.orderRepo.update(
      { id: orderId },
      {
        status: NSOrder.EStatus.PENDING,
        totalPrice: amount,
        totalPriceVat: amountVat,
        vat,
      },
    );

    // 4. If Stripe then create checkout session
    if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
      // Create checkout session
      const checkoutSession = await this.createStripeCheckoutSession({
        memberId: member.id,
        orderId,
        transactionId,
        customerEmail: member.email,
        newPlanId: currentPlan.id,
        paymentType: unit,
        memberPackageId: currentPackage.id,
        isAutoRenewal,
        amount: +amountVat,
        timeRegister: 1,
        transactionType: NSPayment.ETransactionType.RENEWAL,
      });

      // Update transaction with sessionId
      if (checkoutSession.sessionId) {
        await this.paymentTransactionRepo.update(transactionId, {
          sessionRefId: checkoutSession.sessionId,
        });
      }

      return {
        ...savedTransaction,
        memberPackage: currentPackage,
        checkoutUrl: checkoutSession?.checkoutUrl,
        sessionId: checkoutSession?.sessionId,
      };
    }
  }

  // Upgrade service package
  async upgradeMemberPackage(
    transaction: CreatePaymentTransactionDto,
    currentPackage: MemberPackageDto,
    newPackage: MemberPackageDto,
  ) {
    const { orderId, paymentProvider, isAutoRenewal, amount, amountVat, unit, timeRegister } =
      transaction;
    //check orderType
    const order = await this.orderRepo.findOne({ where: { id: orderId } });
    if (!order) throw new BusinessException('payment.order_not_found');
    if (order.orderType !== NSOrder.EOrderType.UPGRADE) {
      throw new BusinessException('Go to Dashboard to view your service package');
    }
    const checkCurrentPackage = await this.memberPackageRepo.findOne({
      where: { id: currentPackage.id, status: NSMember.EMemberPackageStatus.ACTIVE },
    });
    if (!checkCurrentPackage) throw new BusinessException('payment.service_package_invalid');
    if (isAutoRenewal && checkCurrentPackage.subscriptionId) {
      // Hủy subscription cũ
      if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
        await this.cancelSubscriptionImmediately(checkCurrentPackage.subscriptionId);
      }
    }

    const currentPlan = await this.packagePlanRepo.findOne({
      where: { id: currentPackage.packagePlanId },
    });
    // Error if the service package you are using has been hard deleted (this is an error that cannot occur, system error)
    if (!currentPlan) throw new BusinessException('payment.service_package_not_found');

    const newPlan = await this.packagePlanRepo.findOne({ where: { id: newPackage.packagePlanId } });
    // Error if the service package you want to upgrade to has been hard deleted (this is an error that cannot occur, system error)
    if (!newPlan) throw new BusinessException('payment.service_package_not_found');

    // Check if it's an upgrade by transaction limit quantity
    if (currentPlan.transactionLimit >= newPlan.transactionLimit) {
      throw new BusinessException(
        'payment.upgrade_service_package_invalid',
      );
    }

    const member = await this.memberRepo.findOne({ where: { id: currentPackage.memberId } });
    if (!member) throw new BusinessException('payment.member_not_found');

    // Update order value and vat
    const vat = +amountVat - +amount;

    // 2. Create payment transaction
    const paymentTransaction = this.paymentTransactionRepo.create({
      ...transaction,
      memberId: member.id,
      amount: amountVat,
      grossAmount: amountVat,
      memberPackageId: newPlan.id,
      transactionType: NSPayment.ETransactionType.UPGRADE,
    });
    const savedTransaction = await this.paymentTransactionRepo.save(paymentTransaction);
    const transactionId = savedTransaction.id;

    // Recalculate order value
    await this.orderRepo.update(
      { id: orderId },
      {
        status: NSOrder.EStatus.PENDING,
        totalPrice: amount,
        totalPriceVat: amountVat,
        vat,
      },
    );

    // 4. If Stripe then create checkout session
    if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
      // Create checkout session
      const checkoutSession = await this.createStripeCheckoutSession({
        memberId: member.id,
        orderId,
        transactionId,
        customerEmail: member.email,
        oldPlanId: currentPlan.id,
        newPlanId: newPlan.id,
        paymentType: unit,
        memberPackageId: currentPackage.id,
        isAutoRenewal,
        amount: +amountVat,
        timeRegister,
        transactionType: NSPayment.ETransactionType.UPGRADE,
      });

      // Update transaction with sessionId
      if (checkoutSession.sessionId) {
        await this.paymentTransactionRepo.update(transactionId, {
          sessionRefId: checkoutSession.sessionId,
        });
      }

      return {
        ...savedTransaction,
        memberPackage: newPackage,
        checkoutUrl: checkoutSession?.checkoutUrl,
        sessionId: checkoutSession?.sessionId,
      };
    }
  }

  // Downgrade service package
  async downgradeMemberPackage(
    transaction: CreatePaymentTransactionDto,
    currentPackage: MemberPackageDto,
    newPackage: MemberPackageDto,
  ) {
    const { orderId, paymentProvider, isAutoRenewal, amount, amountVat, unit, timeRegister } =
      transaction;

    //check orderType
    const order = await this.orderRepo.findOne({ where: { id: orderId } });
    if (!order) throw new BusinessException('payment.order_not_found');
    if (order.orderType !== NSOrder.EOrderType.DOWNGRADE) {
      throw new BusinessException('payment.order_type_invalid');
    }

    const checkCurrentPackage = await this.memberPackageRepo.findOne({
      where: { id: currentPackage.id, status: NSMember.EMemberPackageStatus.ACTIVE },
    });
    if (!checkCurrentPackage) throw new BusinessException('payment.service_package_invalid');
    if (isAutoRenewal && checkCurrentPackage.subscriptionId) {
      // Hủy subscription cũ
      if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
        await this.cancelSubscriptionImmediately(checkCurrentPackage.subscriptionId);
      }
    }

    const currentPlan = await this.packagePlanRepo.findOne({
      where: { id: currentPackage.packagePlanId },
    });
    // Error if the service package you are using has been hard deleted (this is an error that cannot occur, system error)
    if (!currentPlan) throw new BusinessException('payment.service_package_not_found');

    const newPlan = await this.packagePlanRepo.findOne({ where: { id: newPackage.packagePlanId } });
    // Error if the service package you want to use has been hard deleted (this is an error that cannot occur, system error)
    if (!newPlan) throw new BusinessException('payment.service_package_not_found');

    // Check if it's a downgrade by transaction limit quantity
    if (currentPlan.transactionLimit <= newPlan.transactionLimit) {
      throw new BusinessException(
        'payment.downgrade_service_package_invalid',
      );
    }

    const member = await this.memberRepo.findOne({ where: { id: currentPackage.memberId } });
    if (!member) throw new BusinessException('payment.member_not_found');

    // 1. Create payment transaction
    const count = await this.paymentTransactionRepo.count();
    // Update order value and vat
    const vat = +amountVat - +amount;

    // 2. Create payment transaction
    const paymentTransaction = this.paymentTransactionRepo.create({
      ...transaction,
      memberId: member.id,
      amount: amountVat,
      grossAmount: amountVat,
      memberPackageId: newPlan.id,
      transactionType: NSPayment.ETransactionType.DOWNGRADE,
    });
    const savedTransaction = await this.paymentTransactionRepo.save(paymentTransaction);
    const transactionId = savedTransaction.id;

    // Recalculate order value
    await this.orderRepo.update(
      { id: orderId },
      {
        status: NSOrder.EStatus.PENDING,
        totalPrice: amount,
        totalPriceVat: amountVat,
        vat,
      },
    );

    // 4. If Stripe then create checkout session
    if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
      // Create checkout session
      const checkoutSession = await this.createStripeCheckoutSession({
        memberId: member.id,
        orderId,
        transactionId,
        customerEmail: member.email,
        oldPlanId: currentPlan.id,
        newPlanId: newPlan.id,
        paymentType: unit,
        memberPackageId: currentPackage.id,
        isAutoRenewal,
        amount: +amountVat,
        timeRegister,
        transactionType: NSPayment.ETransactionType.DOWNGRADE,
      });

      // Update transaction with sessionId
      if (checkoutSession.sessionId) {
        await this.paymentTransactionRepo.update(transactionId, {
          sessionRefId: checkoutSession.sessionId,
        });
      }

      return {
        ...savedTransaction,
        memberPackage: newPackage,
        checkoutUrl: checkoutSession?.checkoutUrl,
        sessionId: checkoutSession?.sessionId,
      };
    }
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.paymentTransactionRepo.find({ where: { memberId } });
  }

  async findOne(id: string) {
    return this.paymentTransactionRepo.findOne(id);
  }

  async findPagination(body: PaymentTransactionListDto) {
    const { code, memberId, orderId, status, ...pageRequest } = body;
    const where: any = {};
    if (code) {
      where.code = code;
    }
    if (memberId) {
      where.memberId = memberId; // Update to use memberContextSession
    }
    if (orderId) {
      where.orderId = orderId;
    }
    if (status) {
      where.status = status;
    }
    return this.paymentTransactionRepo.findPagination(
      { where, order: { createdDate: 'DESC' } },
      pageRequest,
    );
  }

  //#region STRIPE
  /**
   * Create Stripe Checkout Session for both payment and subscription
   */
  @DefTransaction()
  async createStripeCheckoutSession(body: PaymentSubscriptionDto) {
    const {
      memberId,
      transactionType,
      orderId,
      transactionId,
      customerEmail,
      newPlanId,
      oldPlanId,
      paymentType,
      memberPackageId,
      isAutoRenewal,
      amount,
      timeRegister,
    } = body;

    const { REDIRECT_URL_PAYMENT } = configEnv();

    // Get plan information
    const plan = await this.packagePlanRepo.findOne({ where: { id: newPlanId } });
    if (!plan) throw new BusinessException('payment.plan_not_found');

    const priceId =
      paymentType === NSPackage.EPlanTypePayment.MONTHLY
        ? plan.stripePriceMonthId
        : plan.stripePriceYearId;

    if (!priceId) throw new BusinessException('payment.price_not_found');

    // Metadata sent to Stripe
    const metaData = {
      memberId,
      orderId,
      transactionId,
      memberPackageId,
      newPlanId,
      oldPlanId,
      paymentType,
      isAutoRenewal: String(isAutoRenewal || false),
      timeRegister: String(timeRegister || 1),
      transactionType,
    };

    const baseSessionConfig = {
      //customer_email: customerEmail,
      success_url: `${REDIRECT_URL_PAYMENT}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${REDIRECT_URL_PAYMENT}`,
      metadata: metaData,
    };

    // Check or create Stripe customer
    let customers = await this.stripe.customers.list({ email: customerEmail });

    let customer: Stripe.Customer;
    if (customers.data.length > 0) {
      // Optional logic: choose first customer, or by metadata
      customer = customers.data.find(c => c.metadata?.memberPackageId === memberPackageId) || customers.data[0];
    } else {
      customer = await this.stripe.customers.create({
        email: customerEmail,
        metadata: { memberPackageId, orderId },
      });
    }

    // 1. One-time payment checkout
    const session = await this.stripe.checkout.sessions.create({
      mode: 'payment',
      customer: customer.id,
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `${plan.name} package ${timeRegister} ${paymentType === 'MONTHLY' ? 'month(s)' : 'year(s)'}`,
            },
            unit_amount: numberHelper.toStripeAmount(+amount),
          },
          quantity: 1,
        },
      ],
      payment_intent_data: {
        setup_future_usage: 'off_session', // Allow reuse in the future (off_session auto charge)
      },
      invoice_creation: { enabled: true },
      ...baseSessionConfig,
    });

    // 2. Create auto-renewal subscription (if enabled)
    if (isAutoRenewal) {
      const start = dayjs(); // Start date
      const nextBillingDate = start.add(
        timeRegister,
        paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year',
      );
      const trialPeriodDay = Math.max(0, Math.min(nextBillingDate.diff(start, 'day'), 730)); // Stripe tối đa 2 năm

      const subscription = await this.stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: priceId }],
        metadata: metaData,
        trial_period_days: trialPeriodDay,
        proration_behavior: 'none',
      });

      await this.memberPackageRepo.update(
        { id: memberPackageId },
        {
          buyPrice: +amount,
          subscriptionId: subscription.id,
          subscriptionProvider: NSPayment.EPaymentProvider.STRIPE,
          isAutoRenewal: true,
          nextPaymentDate: nextBillingDate.toDate(),
        },
      );
    }

    // 3. Update transaction
    await this.paymentTransactionRepo.update(transactionId, {
      sessionRefId: session.id,
    });

    return {
      checkoutUrl: session.url,
      sessionId: session.id,
    };
  }

  /**
   *
   * @param invoiceId
   * @returns
   */
  async getInvoiceDetails(body: InvoiceDetailDto) {
    const { invoiceId } = body;
    try {
      // Check if invoiceId exists in any order
      const order = await this.orderRepo.findOne({ where: { invoiceId } });
      if (!order) throw new BusinessException('payment.order_not_found');

      const invoice = await this.stripe.invoices.retrieve(invoiceId);

      return {
        id: invoice.id,
        status: invoice.status,
        amount_due: invoice.amount_due,
        amount_paid: invoice.amount_paid,
        customer_email: invoice.customer_email,
        hosted_invoice_url: invoice.hosted_invoice_url,
        invoice_pdf: invoice.invoice_pdf, // 🔗 PDF download link
        created: invoice.created,
        due_date: invoice.due_date,
      };
    } catch (error) {
      console.error(`Error fetching invoice: ${error.message}`);
      throw new BusinessException('payment.invoice_not_found');
    }
  }

  /**
   * 
   * @param subscriptionId 
   * @returns 
   */
  async cancelSubscriptionImmediately(subscriptionId: string) {
    const subscription = await this.stripe.subscriptions.cancel(subscriptionId);
    return subscription;
  }
  //#endregion
}
