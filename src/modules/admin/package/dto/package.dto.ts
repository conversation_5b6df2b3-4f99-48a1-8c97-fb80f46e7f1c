import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsInt,
  Min,
  IsBoolean,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSPackage } from '~/common/enums/package.enum';

export class CreatePackagePlanDto {
  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsNumber()
  @Min(0)
  originalPrice: number;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> bán theo tháng' })
  @IsNumber()
  @Min(0)
  sellPriceMonthly: number;

  @ApiProperty({ description: 'Gi<PERSON> bán theo năm' })
  @IsNumber()
  @Min(0)
  sellPriceYearly: number;

  @ApiProperty({ description: '<PERSON><PERSON> lượng giao dịch cho phép (năm)' })
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  transactionLimitYearly: number;

  @ApiProperty({ description: 'Số lượng cấu hình cho phép (năm)' })
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  configLimitYearly: number;

  @ApiProperty({ description: 'Số lượng giao dịch cho phép' })
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  transactionLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình cho phép' })
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  configLimit: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ theo tháng' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ theo năm' })
  @IsOptional()
  @IsString()
  descriptionYearly?: string;

  @ApiPropertyOptional({ description: 'Ghi chú / miêu tả chung cho gói' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Là phổ biến nhất' })
  @IsOptional()
  @IsBoolean()
  isMostPopular?: boolean;

  @ApiPropertyOptional({ description: 'Là gói dùng thử' })
  @IsOptional()
  @IsBoolean()
  isTrial?: boolean;

  @ApiPropertyOptional({ description: 'Số lượng byte được phép', example: '1000000000' })
  @IsNotEmpty()
  byteLimit?: number;

  @ApiPropertyOptional({ description: 'Số lượng byte được phép (năm)', example: '1000000000' })
  @IsNotEmpty()
  byteLimitYearly?: number;

  @ApiPropertyOptional({ description: 'Số lượng project (member-key) được phép', example: 10 })
  @IsNotEmpty()
  projectLimit?: number;

  @ApiPropertyOptional({ description: 'Số lượng project (member-key) được phép (năm)', example: 10 })
  @IsNotEmpty()
  projectLimitYearly?: number;
}

export class UpdatePackagePlanDto extends CreatePackagePlanDto {
  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsNotEmpty()
  @IsString()
  id: string;

  stripePriceYearId?: string;
  stripePriceMonthId?: string;
}

export class PackagePlanListDto extends PageRequest {
  //code
  @ApiProperty({ description: 'Mã gói dịch vụ' })
  @IsOptional()
  code?: string;

  //memberId
  @ApiProperty({ description: 'ID của thành viên' })
  @IsOptional()
  memberId?: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Giá gốc' })
  @IsOptional()
  originalPrice?: number;

  @ApiProperty({ description: 'Giá bán theo tháng' })
  @IsOptional()
  sellPriceMonthly?: number;

  @ApiProperty({ description: 'Giá bán theo năm' })
  @IsOptional()
  sellPriceYearly?: number;

  @ApiProperty({ description: 'Số lượng giao dịch cho phép' })
  @IsOptional()
  transactionLimit?: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ' })
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSPackage.EStatus;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateFrom?: Date;

  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDateTo?: Date;
}
