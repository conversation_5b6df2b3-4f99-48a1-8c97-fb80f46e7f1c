import { Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSPackage } from '~/common/enums/package.enum';

@Entity('package_plan')
@Index(['code'], { unique: true })
export class PackagePlanEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã gói dịch vụ' })
  @Column({ type: 'varchar', length: 255 })
  code: string;

  @ApiProperty({ description: 'Tên gói dịch vụ' })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> gốc (Đơn giá theo tháng)' })
  @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
  originalPrice: number;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> bán theo tháng' })
  @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
  sellPriceMonthly: number;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> bán theo năm' })
  @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
  sellPriceYearly: number;

  @ApiProperty({ description: 'Số lượng giao dịch được phép (tháng)' })
  @Column({ type: 'int' })
  transactionLimit: number;

  @ApiProperty({ description: 'Số lượng cấu hình được phép (tháng)' })
  @Column({ type: 'int' })
  configLimit: number;

  @ApiProperty({ description: 'Số lượng giao dịch được phép (năm)' })
  @Column({ type: 'int' })
  transactionLimitYearly: number;

  @ApiProperty({ description: 'Số lượng cấu hình được phép (năm)' })
  @Column({ type: 'int' })
  configLimitYearly: number;

  @ApiProperty({ description: 'Số lượng byte' })
  @Column({ type: 'bigint', nullable: true })
  byteLimit: number;

  @ApiProperty({ description: 'Số lượng byte được phép (năm)' })
  @Column({ type: 'bigint', nullable: true })
  byteLimitYearly: number;

  @ApiProperty({ description: 'Số lượng project (member-key) ' })
  @Column({ type: 'int', nullable: true, default: 0 })
  projectLimit: number;

  @ApiProperty({ description: 'Số lượng project (member-key) được phép (năm)' })
  @Column({ type: 'int', nullable: true, default: 0 })
  projectLimitYearly: number;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ' })
  @Column({ type: 'varchar', nullable: true })
  description?: string;

  @ApiPropertyOptional({ description: 'Mô tả gói dịch vụ theo năm' })
  @Column({ type: 'varchar', nullable: true })
  descriptionYearly?: string;

  @ApiPropertyOptional({ description: 'Ghi chú / Miêu tả chung cho gói' })
  @Column({ type: 'varchar', nullable: true })
  note?: string;

  @ApiPropertyOptional({ description: 'Là phổ biến nhất' })
  @Column({ type: 'boolean', default: false })
  isMostPopular?: boolean;

  @ApiPropertyOptional({ description: 'Trạng thái' })
  @Column({ type: 'varchar', length: 20, default: NSPackage.EStatus.ACTIVE })
  status?: NSPackage.EStatus;

  @ApiPropertyOptional({ description: 'Stripe Price ID theo Tháng (cho subscription)' })
  @Column({ type: 'varchar', nullable: true })
  stripePriceMonthId?: string;

  @ApiPropertyOptional({ description: 'Stripe Price ID theo Năm (cho subscription)' })
  @Column({ type: 'varchar', nullable: true })
  stripePriceYearId?: string;

  @ApiPropertyOptional({ description: 'Là gói dùng thử' })
  @Column({ type: 'boolean', default: false })
  isTrial?: boolean;

  @ApiPropertyOptional({ description: 'Thứ tự hiển thị trong danh sách (gói nào lên trước)' })
  @Column({ type: 'int', default: 0 })
  displayOrder?: number;

  @ApiPropertyOptional({ description: 'Gói ẩn (không public trên UI)' })
  @Column({ type: 'boolean', default: false })
  isHidden?: boolean;
}
